<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class SetupQueueDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:setup-database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the separate queue database with required tables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up queue database...');
        
        try {
            // Test connection to the queue database
            DB::connection('queue')->getPdo();
            $this->info('Successfully connected to queue database.');
            
            // Run the migration to create queue tables
            $this->info('Creating queue tables...');
            Artisan::call('migrate', [
                '--path' => 'database/migrations/2025_04_06_000000_create_queue_tables.php',
                '--force' => true,
            ]);
            
            $this->info('Queue database setup completed successfully!');
            $this->info('Queue connection: ' . config('queue.default'));
            $this->info('Queue database: ' . config('database.connections.queue.database'));
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to set up queue database: ' . $e->getMessage());
            
            $this->info("\nPlease make sure your .env file has the following variables:");
            $this->info('QUEUE_CONNECTION=queue-database');
            $this->info('QUEUE_DB_HOST=your_host');
            $this->info('QUEUE_DB_PORT=3306');
            $this->info('QUEUE_DB_DATABASE=rteapineww3creat_data2');
            $this->info('QUEUE_DB_USERNAME=your_username');
            $this->info('QUEUE_DB_PASSWORD=your_password');
            
            return 1;
        }
    }
}
