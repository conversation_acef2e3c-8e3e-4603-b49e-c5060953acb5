<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    
    'shopify' => [
        'webhook_secret' => env('SHOPIFY_WEBHOOK_SECRET'),
        'api_key' => env('SHOPIFY_API_KEY'),
        'api_secret' => env('SHOPIFY_API_SECRET'),
        'store' => env('SHOPIFY_STORE'),
        'access_token' => env('SHOPIFY_ACCESS_TOKEN'),
        'scope' => env('SHOPIFY_ACCESS_SCOPE'),
        'api_version' => env('SHOPIFY_API_VERSION'),
    ],
    
    'garmin' => [
        'consumer_key' => env('GARMIN_CONSUMER_KEY'),
        'consumer_secret' => env('GARMIN_CONSUMER_SECRET'),
    ],
    
    'strava' => [
        'api_url' => env('STRAVA_API_BASE_URL'),
        'client_id' => env('STRAVA_CLIENT_ID'),
        'redirect_url' => env('STRAVA_REDIRECT_URI'),
        'client_secret' => env('STRAVA_CLIENT_SECRET'),
        'webhook_verification_code' => env('STRAVA_WEBHOOK_VERIFICATION_CODE'),
    ],
    'tracker' => [
        'workflow_url' => env('TRACKER_USER_POINT_WORKFLOW'),
    ],
];
