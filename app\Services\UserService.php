<?php

namespace App\Services;

use App\Repositories\UserRepository;
use Illuminate\Support\Str;
use App\Models\Event;
use Carbon\Carbon;

class UserService
{
    public function __construct(
        protected UserRepository $userRepository
    ) {
    }
    
    public function find($id)
    {
        return $this->userRepository->find($id);
    }
    
    public function basic($request)
    {
        return $this->userRepository->basic($request);
    }
    
    public function profile($request)
    {
        return $this->userRepository->profile($request);
    }
    
    public function achievements($event, $dateRange, $user){
        return $this->userRepository->achievements($event, $dateRange, $user);
    }
    
    public function createShopifyUser($sopifyOrder, $data, $metafields){
        
        if(!$data || !(isset($metafields['subtitle']))) {
            return false;
        }
        
        
        //$event = Event::where('name',$metafields['subtitle'])->latest()->first();
        
        $subtitles = explode("|", $metafields['subtitle']);

        
        $userData = [];
     
        foreach($data as $item) {
            $key = null;
            if(Str::contains($item['name'], 'mail')){
               $key = 'email';
           } else if(Str::contains($item['name'], 'rstName')){
               $key = 'first_name';
           } else if(Str::contains($item['name'], 'astName')){
               $key = 'last_name';
           }
           
           if($key == null) {
               continue;
           }
           
           $userData[$key] = $item['value'];
        }
        
        if(!$userData || !isset($userData['email']) || !$userData['email']) {
            
            if(!$sopifyOrder) {
                return false;
            }
            
            $userData = [
                'email' => $sopifyOrder->email,
                'first_name' => $sopifyOrder->first_name,
                'last_name' => $sopifyOrder->last_name
            ];
        }
        
        $user = $this->userRepository->findByEmail($userData['email']);
        
        if(!$user) {
            $userData['display_name'] = implode(' ', [$userData['first_name'], $userData['last_name']]);
            $userData['email'] = strtolower($userData['email']);
            $userData['encrypted_password'] = "gfh19ab";
            $userData['preferred_event_id'] = 0;
            
            $user = $this->userRepository->createShopifyUser($userData);
        }
            
        foreach ($subtitles as $subtitle) { 
            $event = Event::where('name', $subtitle)->latest()->first();
            
            if($event) {
                if(!$user->preferred_event_id){
                $user->fill(['preferred_event_id' => $event->id])->save();
                }
                $hasEvent = $user->participations()->where(['event_id' => $event->id])->count();
                
                if(!$hasEvent) {
                    
                    $user->participations()->create([
                        'event_id' => $event->id,
                        'subscription_start_date' => $event->start_date,
                        'subscription_end_date' => $event->end_date
                    ]);

                    if($event->id == 2){
                        $user->participations()->where(['event_id' => $event->id])->update(['subscription_start_date' => Carbon::now()->format('Y-m-d')]);
                    }
                }
            }
        }
        
        return $user;
        
    }
    
    public function timezones(){
         return  config('timezones.timezone');
    }
}