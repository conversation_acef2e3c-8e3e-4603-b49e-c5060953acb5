<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamMembership extends Model
{
    
    public $timestamps = false;
    
    protected $guarded = [];
    
    public function user(){
        return $this->belongsTo(User::class);
    }
    
     public function team(){
        return $this->belongsTo(Team::class);
    }

}
