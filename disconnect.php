<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class DeviceDeregistrationService
{
    private string $fitbit_client_id;
    private string $fitbit_client_secret;
    private string $strava_client_id;
    private string $strava_client_secret;
    private string $garmin_consumer_key;
    private string $garmin_consumer_secret;
    
    public function __construct()
    {
        $this->fitbit_client_id = config('services.fitbit.client_id');
        $this->fitbit_client_secret = config('services.fitbit.client_secret');
        $this->strava_client_id = config('services.strava.client_id');
        $this->strava_client_secret = config('services.strava.client_secret');
        $this->garmin_consumer_key = config('services.garmin.consumer_key');
        $this->garmin_consumer_secret = config('services.garmin.consumer_secret');
    }
    
    /**
     * Central function to deregister from any device
     */
    public function deregisterDevice(string $device_type, array $user_tokens): array
    {
        return match (strtolower($device_type)) {
            'fitbit' => $this->deregisterFitbit($user_tokens),
            'strava' => $this->deregisterStrava($user_tokens),
            'garmin' => $this->deregisterGarmin($user_tokens),
            default => ['success' => false, 'error' => 'Unsupported device type']
        };
    }
    
    /**
     * Deregister Fitbit app
     */
    public function deregisterFitbit(array $tokens): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($this->fitbit_client_id . ':' . $this->fitbit_client_secret),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ])->post('https://api.fitbit.com/oauth2/revoke', [
                'token' => $tokens['access_token']
            ]);
            
            Log::info('Fitbit deregistration response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return [
                'success' => $response->successful(),
                'response' => $response->body(),
                'http_code' => $response->status()
            ];
            
        } catch (Exception $e) {
            Log::error('Fitbit deregistration failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Deregister Strava app
     */
    public function deregisterStrava(array $tokens): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $tokens['access_token']
            ])->post('https://www.strava.com/oauth/deauthorize');
            
            Log::info('Strava deregistration response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return [
                'success' => $response->successful(),
                'response' => $response->body(),
                'http_code' => $response->status()
            ];
            
        } catch (Exception $e) {
            Log::error('Strava deregistration failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Deregister Garmin app
     */
    public function deregisterGarmin(array $tokens): array
    {
        try {
            $url = "https://apis.garmin.com/wellness-api/rest/user/registration";
            
            $oauth_params = [
                'oauth_consumer_key' => $this->garmin_consumer_key,
                'oauth_token' => $tokens['access_token'],
                'oauth_signature_method' => 'HMAC-SHA1',
                'oauth_timestamp' => time(),
                'oauth_nonce' => uniqid(),
                'oauth_version' => '1.0'
            ];
            
            $signature = $this->generateOAuthSignature('DELETE', $url, $oauth_params, $tokens['access_token_secret']);
            $oauth_params['oauth_signature'] = $signature;
            
            $auth_header = 'OAuth ' . http_build_query($oauth_params, '', ', ');
            
            $response = Http::withHeaders([
                'Authorization' => $auth_header
            ])->delete($url);
            
            Log::info('Garmin deregistration response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return [
                'success' => in_array($response->status(), [200, 204]),
                'response' => $response->body(),
                'http_code' => $response->status()
            ];
            
        } catch (Exception $e) {
            Log::error('Garmin deregistration failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generate OAuth signature for Garmin
     */
    private function generateOAuthSignature(string $method, string $url, array $params, string $token_secret): string
    {
        ksort($params);
        $param_string = http_build_query($params, '', '&', PHP_QUERY_RFC3986);
        
        $base_string = strtoupper($method) . '&' . 
                      rawurlencode($url) . '&' . 
                      rawurlencode($param_string);
        
        $signing_key = rawurlencode($this->garmin_consumer_secret) . '&' . rawurlencode($token_secret);
        
        return base64_encode(hash_hmac('sha1', $base_string, $signing_key, true));
    }
}