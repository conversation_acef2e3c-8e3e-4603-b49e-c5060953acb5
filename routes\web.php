<?php

use Illuminate\Support\Facades\Route;
use GuzzleHttp\Client;

use App\Http\Controllers\{
    TrackerLoginsController,
    FitbitAuthController,
    GarminAuthController
};

use App\Http\Controllers\Shopify\{
    WebhooksController,
    OrdersController
};
use App\Http\Controllers\Webhook\{
    TrackersController,
    TestTrackersController,
    HubspotsController,
    UserActivitiesController,
    RubyCronController,
    UserPointWorkflowsController
};

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::group(['prefix' => 'tracker'], function(){
    Route::get('oauth/strava', [TrackerLoginsController::class,'redirectToAuthUrl'])->name('strava.oauth');
    Route::get('login',[TrackerLoginsController::class, 'index'])->name('tracker.login');
    Route::get('logout',[TrackerLoginsController::class, 'index'])->name('tracker.logut');
    Route::get('user/activities',[TrackerLoginsController::class, 'userActivities'])->name('tracker.user.activities');
    Route::get('strava/callback',[TrackerLoginsController::class, 'stravaCallback']);
    
    Route::get('/fitbit/auth/{state?}', [FitbitAuthController::class, 'redirectToFitbit'])->name('fitbit.auth');
    Route::get('/oauth/fitbit/{state?}', [FitbitAuthController::class, 'redirectToFitbit'])->name('fitbit.oauth');
    Route::get('/fitbit/callback', [FitbitAuthController::class, 'handleCallback'])->name('fitbit.callback');
    Route::get('/fitbit/refresh', [FitbitAuthController::class, 'refreshToken'])->name('fitbit.refresh');
    Route::get('/oauth/garmin/{state?}', [GarminAuthController::class, 'redirectToGarmin'])->name('garmin.oauth');
    Route::get('garmin/callback',[GarminAuthController::class, 'handleCallback'])->name('garmin.callback');
});

Route::group(['prefix' => 'shopify'], function(){
    Route::post('/webhooks/orders', [WebhooksController::class, 'handleOrderCreation']);
    Route::get('/cron/orders', [OrdersController::class, 'getOrders']);
});

Route::group(['prefix' => 'webhook'], function(){
   Route::get('/user-point-workflow/trigger', [UserPointWorkflowsController::class,'triggerWorkFlow']);
   Route::post('v1/tracker/fitbit', [TrackersController::class,'fitbitTracker']); 
   Route::get('v1/tracker/fitbit', [TrackersController::class,'fitbitVerify']); 
   Route::post('hubspot/user/verification', [HubspotsController::class,'verifyUserEmail']); 
   
   Route::get('user/activity/distances/tracker', [UserActivitiesController::class,'userDistanceTracker']);
   
   Route::get('event/trigger-celebration-mail', [UserActivitiesController::class,'triggerCelebrationMail']);
   
   #Route::get('tracker/fitbit/user/distances', [TrackersController::class,'fitBitUserDistanceTracker']); 
   Route::get('tracker/fitbit/user/manual/distances', [TrackersController::class,'fitBiUserManualDistanceTracker']); 
   
    Route::get('v1/tracker/fitbit/test', [TrackersController::class,'testfitbit']); 
    
    Route::get('/user/hubspot-contact/verify',[OrdersController::class,'userHubspotVerification']);
    
    Route::post('tracker/garmin/pings', [GarminAuthController::class, 'handleWebhook']);
    Route::any('tracker/strava/pings',[TrackerLoginsController::class, 'handleWebhook'])->name('webhook.strava');

    
    Route::post('test/v1/tracker/fitbit', [TestTrackersController::class,'fitbitTracker']); 
    Route::get('test/v1/tracker/fitbit', [TestTrackersController::class,'fitbitVerify']);
    #Route::get('test/tracker/fitbit/user/distances', [TestTrackersController::class,'fitBitUserDistanceTracker']); 
    Route::get('test/tracker/fitbit/user/manual/distances', [TestTrackersController::class,'fitBiUserManualDistanceTracker']); 
    Route::get('test/v1/tracker/fitbit/test', [TestTrackersController::class,'testfitbit']); 
    Route::get('ruby/process-user-points/{event_id}',[RubyCronController::class, 'processUserPointsFromRuby']);

  
});

Route::get('/shopify/orders', [OrdersController::class,'orderList']);
Route::post('/shopify/orders', [OrdersController::class,'orderList']);

Route::get('test/workflow', function(){
       $response  = \Illuminate\Support\Facades\Http::withQueryParameters([
            'user_id' => 165689,
            'event_id' => 2,
        ])
        ->post(config('services.tracker.workflow_url'));
        
    dd($response->json());
});

Route::get('testfile', function(){
    
    //dd(getEnv('TRACKER_API_URL'),env('TRACKER_API_URL'));
    
    //(new \App\Services\MailService)->sendPasswordResetEmail('<EMAIL>');
    //https://staging-tracker.runtheedge.com/api/v1/event_milestone_images?event_id=66&distance=3.1&activity_id=1
     $response = \Illuminate\Support\Facades\Http::get('https://staging-tracker.runtheedge.com/api/v1/event_milestone_images', ['event_id'=>66,'distance'=>"3.1",'activity_id'=>1]);
     dd($response->json('data'));
    return null;
    $event = \App\Models\Event::find(66);
    
    $fitLife = \App\Models\FitLifeActivityRegistration::where('date','2021-05-11')->first();
    //where("activity_id = ? and total_points <= ?", activity.id, distance).order(total_points: :desc).limit(1)
    dd($fitLife->activity->milestones()->where('total_points','<=',5)->orderBy('total_points','desc')->first());
    
    dd(route('fitbit.auth','app'), route('strava.auth'));
    $accessToken = request()->get('access_token');
    $response = (new \App\Services\StravaService($accessToken))->activities(request()->get('date'));
    
    return $response;
    dd($response);
    $currentTime = \Carbon\Carbon::now()->subMinutes(5);
   // dd($currentTime);
    $t = \App\Models\ShopifyOrder::where('created_at','<=',$currentTime)
    ->whereYear('created_at', Carbon\Carbon::now()->format('Y'))->whereNull('hubspot_status')->limit(5)->get();
    
    
    
   return $t;
    dd($a);
//$user = \App\Models\User::where('email','<EMAIL>')->first();
//$user->fill(['encrypted_password' => 'test@123'])->save();
//dd($user);

$user =  \App\Models\User::find(1);
 $membershipRequest = $user->invites()->with(['team' => function($query){
            return $query;    
        }])->first();
        
        dd($membershipRequest->team->memberships()->count());
         
        
        $eventId = 64;
        
       return \App\Models\Team::whereHas('invites', function($query) use($user, $eventId){
            return $query->where('prospective_member_id',$user->id)->where('event_id',$eventId);
        })->get();

return \App\Models\TeamMembershipInvite::select(['status'])->groupBy('status')->get();
    dd(\Carbon\Carbon::now()->format('Y-m-d H:i:s.u'));
});
Route::get('/test/points', function (\App\Services\HubspotService $eventService) {
    
    
    $user = (object)['email'=>'<EMAIL>','first_name' => 'HTest','last_name' => 'HTest1','product_sku' => '2025-Kids-Digital-Tracker'];
    dd($eventService->existsOrCreate($user, true));
    
    $sourceProfile = $user->profiles()->where('data_source_id', 2)->first();
    
    $httpClient = new Client([
        'base_uri' => 'https://api.fitbit.com/1/',
        'headers' => [
            'Authorization' => sprintf('Bearer %s', $sourceProfile->access_token),
            'Accept' => 'application/json',
        ],
    ]);
    
    $date = "2024-11-21";
        
    $response = $httpClient->get("user/-/activities/date/{$date}.json");
    $data = json_decode($response->getBody()->getContents(), false);
    
    
    $distance = collect($data->summary->distances)->filter(function($distance){
        return in_array($distance->activity, ['total','tracker','loggedActivities']);
    })->sum('distance');
    

    $participations = $user->participations()->get();
    
    foreach($participations as $participation){
        $pointdata = ['amount' => $distance,'date' => $date,'event_id' => $participation->event_id,'modality' => 'other','data_source_id' => $sourceProfile->data_source_id];
          
        $userPoint = $user->points()->where(['date' => $date,'modality' => 'other','event_id' => $participation->event_id])->first();

        if($userPoint) {
            $userPoint->update($pointdata);
            continue;
        } 
        
        $user->points()->create($pointdata);
        
        $eventService->createOrUpdateUserPoint($user, $participation->event_id, $request->date);
    }
    dd($distance);  
   // return $data;
    
    return ($data->summary->distances);
    
    $event = \App\Models\Event::find(2);
    
    //$eventService = (new \App\Services\EventService);
    
    $weeklyPointTotal = $eventService->calculateUserWeeklyPoints($user,$event);
    $pointTotal = $eventService->calculateUserTotalPoints($user,$event);
    $monthlyPointTotal = $eventService->calculateUserMonthlyPoints($user,$event);
    
    $a = $eventService->updateTeamPoint($user, $event->id,'2024-11-28');
    dd($a);
    dd($weeklyPointTotal,$pointTotal,$monthlyPointTotal);
    
    return ($user->points()->get());
});

Route::get('gmdata', function(App\Services\GarminService $garmin){
    $data = $garmin->getDailies('02-05-2025', '03-05-2025', 'a34e18a6-2473-42a4-b8e6-bb8df5061fd8', '1N0baQBO247cGPk8iKd0w4KvEVnPA4HZm96');
    dd($data->json());
});