<?php

namespace App\Services;

use App\Models\DataSourceProfile;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class GarminService
{
    private string $consumerKey;

    private string $consumerSecret;

    // private string $redirectUrl;

    private string $healthApiUrl = 'https://apis.garmin.com/wellness-api/rest';

    private bool $isDailyStepsEnabled = false;

    public function __construct()
    {
        $this->consumerKey = config('services.garmin.consumer_key');
        $this->consumerSecret = config('services.garmin.consumer_secret');
        // $this->redirectUrl = config('services.garmin.redirect');
    }

    public function processBackfillDailies($startDate, $endDate, $accessToken, $accessTokenSecret)
    {
        $startTimeInSeconds = Carbon::parse($startDate)->timestamp;
        $endTimeInSeconds = Carbon::parse($endDate)->timestamp;
        
        $userId = DataSourceProfile::where('access_token', $accessToken)->first();
        $user = User::find($userId->user_id);

        $participations = $user->participations()
                            ->where('subscription_end_date','>=',$endDate)
                            ->where('include_daily_steps', true)
                            ->whereHas('event', function($query) use($endDate) {
                                return $query->where('start_date','<=', $endDate);
                            })->count();

        Log::debug("Garmin Service: Participations: ", [$participations]);

        if( $participations > 0 ) {
            Log::debug("Garmin Service: Processing Dailies: ", [$startTimeInSeconds, $endTimeInSeconds, $accessToken, $accessTokenSecret]);
            $dailiesUrl = $this->healthApiUrl.'/backfill/dailies';
            $this->processCallbackUrl($startTimeInSeconds, $endTimeInSeconds, $accessToken, $accessTokenSecret, $dailiesUrl);
        }
        
        Log::debug("Garmin Service: Processing Activities: ", [$startTimeInSeconds, $endTimeInSeconds, $accessToken, $accessTokenSecret]);
        $activitiesUrl = $this->healthApiUrl.'/backfill/activities';
        $this->processCallbackUrl($startTimeInSeconds, $endTimeInSeconds, $accessToken, $accessTokenSecret, $activitiesUrl);

        return $this;
    }
 
    public function getResponseStatus()
    {
        return $this->responseStatus;
    }

    private function processCallbackUrl($startTimeInSeconds, $endTimeInSeconds, $accessToken, $accessTokenSecret, $callbackUrl)
    {
        // Query parameters must be included in signature generation
        $queryParams = [
            'summaryStartTimeInSeconds' => $startTimeInSeconds,
            'summaryEndTimeInSeconds' => $endTimeInSeconds,
        ];

        $oauthParams = [
            'oauth_consumer_key' => $this->consumerKey,
            'oauth_nonce' => $this->generateNonce(),
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $this->getTimestamp(),
            'oauth_version' => '1.0',
            'oauth_token' => $accessToken,
        ];

        // Merge all parameters for signature generation
        $params = array_merge($queryParams, $oauthParams);

        // Generate signature
        $signature = $this->createSignature('GET', $callbackUrl, $params, $accessTokenSecret);
        $oauthParams['oauth_signature'] = $signature;

        // Build authorization header
        $authHeader = $this->buildAuthorizationHeader($oauthParams);

        // Make the request with query parameters in URL
        $fullUrl = $callbackUrl.'?'.http_build_query($queryParams);
        $response = Http::withHeaders(['Authorization' => $authHeader])->get($fullUrl);

        $this->responseStatus = $response->status();

        return $this;
    }

    public function getBackfillDailies($startDate, $endDate, $accessToken = '', $accessTokenSecret = '')
    {
        $startTimeInSeconds = Carbon::parse($startDate)->timestamp;
        $endTimeInSeconds = Carbon::parse($endDate)->timestamp;

        $queryParams = [
            'summaryStartTimeInSeconds' => $startTimeInSeconds,
            'summaryEndTimeInSeconds' => $endTimeInSeconds,
        ];

        return $this->makeGarminRequest(
            'GET',
            $this->generateApiUrl('backfill/dailies'),
            $queryParams,
            $accessToken,
            $accessTokenSecret
        );
    }
    
    public function getDailies($startDate, $endDate, $accessToken = '', $accessTokenSecret = '')
    {
        $startTimeInSeconds = 1744934400;
        $endTimeInSeconds = 1745020799;

        $queryParams = [
            'uploadStartTimeInSeconds' => $startTimeInSeconds,
            'uploadEndTimeInSeconds' => $endTimeInSeconds,
        ];

        return $this->makeGarminRequest(
            'GET',
            $this->generateApiUrl('activities'),
            $queryParams,
            $accessToken,
            $accessTokenSecret
        );
    }

    public function processWebhookData($request, $eventService)
    {
        $garminData = [];

        Log::debug("\n\n====================================== Garmin Webhook Data ======================================\n\n");
        Log::debug("Received Webhook Data: ", [$request->all()]);
        /**
        if ($request->has('dailies') || $request->has('activities')) {
            $garminData = ! empty($request->activities) ? $request->activities : $request->dailies;
        }
        */
        $garminData = array_values($request->all());
        $garminData = array_shift($garminData);
        
        Log::debug("GarminData: ", [$garminData]);

        $processedActivities = 0;

        if (! empty($garminData)) {
            foreach ($garminData as $data) {
                if (! isset($data['userAccessToken']) || ! isset($data['callbackURL'])) {
                    Log::error('Missing required fields in Garmin activity data', ['data' => $data]);

                    continue;
                }

                // Find the data source profile using userAccessToken
                $sourceProfile = DataSourceProfile::where('access_token', $data['userAccessToken'])->first();
                // dd($sourceProfile);

                if (! $sourceProfile) {
                    Log::error('Data source profile not found for token', ['token' => $data['userAccessToken']]);

                    continue;
                }

                // Get the user associated with this profile
                $user = $sourceProfile->user;

                if (! $user) {
                    Log::error('User not found for data source profile', ['profile_id' => $sourceProfile->id]);

                    continue;
                }

                $isDailiesCallback = $this->isCallbackDailies($data['callbackURL']);

                // Fetch activity data from Garmin API using the callback URL
                try {
                    $fullCallbackURL = $data['callbackURL'];

                    $scheme = parse_url($fullCallbackURL, PHP_URL_SCHEME);
                    $host = parse_url($fullCallbackURL, PHP_URL_HOST);
                    $path = parse_url($fullCallbackURL, PHP_URL_PATH);

                    $callbackUrl = $scheme.'://'.$host.$path;

                    $queryParamsString = parse_url($fullCallbackURL, PHP_URL_QUERY);
                    $queryParams = [];
                    parse_str($queryParamsString, $queryParams);

                    $activities = $this->makeGarminRequest('GET', $callbackUrl, $queryParams, $sourceProfile->access_token, $sourceProfile->access_token_secret);
                    /**
                     * array:1 [
                     * 0 => array:13 [
                     * "summaryId" => "18882993716"
                     * "activityId" => 18882993716
                     * "activityName" => "Running"
                     * "durationInSeconds" => 60
                     * "startTimeInSeconds" => 1745157090
                     * "startTimeOffsetInSeconds" => 19800
                     * "activityType" => "RUNNING"
                     * "averageSpeedInMetersPerSecond" => 29.505
                     * "averagePaceInMinutesPerKilometer" => 0.564876
                     * "deviceName" => "Unknown"
                     * "distanceInMeters" => 1770.28
                     * "manual" => true
                     * "isWebUpload" => false
                     * ]
                     * ]
                     */

                    $activityData = collect($activities->json())->map(function ($activity) {
                        if (isset($activity['distanceInMeters'])) {
                            $timestamp = ($activity['startTimeInSeconds'] + $activity['startTimeOffsetInSeconds']);
                            $date = Carbon::createFromTimestamp($timestamp)->format('Y-m-d');
                            $distance = round(($activity['distanceInMeters'] / 1609.344), 3);
                            $modality = $this->getModality($activity['activityType']);
                            $time = $activity['startTimeInSeconds'];
                            $summaryId = isset($activity['summaryId']) ? $activity['summaryId'] : null;
                            $modalityType = (isset($activity['manual']) && $activity['manual'] == true) ? 'manual' : 'auto';

                            return compact('date', 'distance', 'modality', 'summaryId', 'modalityType');
                        }
                    })->toArray();

                    $formattedActivities = $this->formatActivities($activityData);
                    Log::debug("Garmin Activity Data: ", [$activities, $activityData, $formattedActivities]);

                    foreach($activityData as $activity) {
                        $this->createPoints($eventService, $user, $activity['date'], $activity['distance'], $sourceProfile, null, $activity['modality'], $activity['summaryId'], $isDailiesCallback, $activity['modalityType']);
                    }

                    // foreach ($activities->json() as $activity) {
                        // $date = Carbon::createFromTimestamp($activity['startTimeInSeconds'])->format('Y-m-d');
                        // $currentDate = Carbon::now()->format('Y-m-d');
                        // $distance = round(($activity['distanceInMeters'] / 1609.344), 3);
                        // $modality = $this->getModality($activity['activityType']);
                        
                        // Log::debug("Garmin Service: Activities: ", [$activity]);
                        
                        // $this->createPoints($eventService, $user, $date, $distance, $sourceProfile, null, $modality);

                        /**
                        $participations = $user->participations()
                            ->where('subscription_end_date', '>=', $currentDate)
                            ->whereHas('event', function ($query) use ($currentDate) {
                                return $query->where('start_date', '<=', $currentDate);
                            })
                            ->get();
                            
                    
                        foreach($participations as $participation) {
                            $pointData = [
                                'amount' => $distance,
                                'date' => $date,
                                'event_id' => $participation->event_id,
                                'modality' => $modality,
                                'data_source_id' => $sourceProfile->data_source_id
                            ];
                            
                            $eventService->createOrUpdateUserPoint($user, $participation->event_id, $date);
                        }
                        */
                    // }

                } catch (Exception $e) {
                    Log::error('Error processing Garmin activity', [
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'data' => $data,
                    ]);

                    continue;
                }

                // Return success response
                return [
                    'status' => 'success',
                    'message' => 'Webhook processed successfully',
                    'activities_processed' => $processedActivities,
                    'code' => 200,
                ];
            }
        }
    }

    public function isCallbackDailies(string $callbackUrl): bool
    {
        return preg_match('/https:\/\/(healthapi|apis)\.garmin\.com\/wellness-api\/rest\/dailies\?/', $callbackUrl) === 1;
    }

    private function createPoints($eventService, $user, $date, $distance, $sourceProfile, $eventId=null, $modality = 'other', $transactionId = null, $isDailiesCallback = false, $modalityType = 'auto')
    {
        if(!$distance){
            return false;
        }

        $currentDate = Carbon::now()->format('Y-m-d');
        
        if($eventId) {
            $participations = $user->participations()->where('event_id', $eventId)->where('subscription_end_date','>=',$date)->whereHas('event', function($query) use($date){
                return $query->where('start_date','<=', $date);
            })->get();
            
        } else {
            $participations = $user->participations()->where('subscription_end_date','>=',$date)->whereHas('event', function($query) use($date){
                return $query->where('start_date','<=', $date);
            })->get();
        }
        
        if(!$participations->count()) {
            return false;
        }
    
        foreach($participations as $participation)
        {   
            if($isDailiesCallback) {
                Log::debug("\n\nParticipation", [$participation]);
                Log::debug("isDailyCallback: ", [$isDailiesCallback]);
                Log::debug("particapation daily steps: ", [$participation->include_daily_steps]);
                Log::debug("\n\n");

                if(!$participation->include_daily_steps) continue;
            }
            
            if ($isDailiesCallback && $participation->include_daily_steps) {
                Log::debug("Garmin Service: Starting 2.5 minute delay for dailies callback", [
                    'user_id' => $user->id,
                    'participation_id' => $participation->id,
                    'event_id' => $participation->event_id,
                    'date' => $date
                ]);
                
                // Sleep for 2.5 minutes (150 seconds) before processing dailies callback
                sleep(150);
                
                Log::debug("Garmin Service: Completed 2.5 minute delay for dailies callback", [
                    'user_id' => $user->id,
                    'participation_id' => $participation->id,
                    'event_id' => $participation->event_id,
                    'date' => $date
                ]);
            }

            if ($isDailiesCallback && $participation->include_daily_steps) {
                $existingPoints = (float) $user->points()->where([
                    'date' => $date,
                    'event_id' => $participation->event_id,
                    'data_source_id' => $sourceProfile->data_source_id
                ])
                ->whereNotIn('modality', ['other'])
                ->where('modality_type', 'auto')
                ->sum('amount');
                
                Log::debug("Points for {$participation->event_id}: ", [round($existingPoints, 4), round($distance, 4)]);

                $distance = $distance - $existingPoints;

                // if($distance - $existingPoints >= 0) {
                //     $distance = $distance - $existingPoints;
                //     Log::debug("If distance: ", [round($distance, 4)]);
                // } else {
                //     $distance = $distance;
                //     Log::debug("Else distance: ", [round($distance, 4)]);
                // }

                // if( ($existingPoints - $distance) >= 0 ) {
                //     $distance = $existingPoints - $distance;
                // }
                
                /**
                if($existingPoints > 0 && $existingPoints < $distance) { // if existing points are less than distance, then subtract the existing points from the distance
                    Log::debug("Post Data (if): Existing Points - {$existingPoints} || Distance - {$distance} || Modality - {$modality}");
                    $distance = $distance - $existingPoints;
                } else if($existingPoints == $distance) { // if existing points are equal to distance
                    Log::debug("Post Data (else if): Existing Points - {$existingPoints} || Distance - {$distance} || Modality - {$modality}");
                    $distance = 0;
                }
                */
            }

            // get current user points
            /**
            if ($isDailiesCallback && $participation->include_daily_steps) {
                $existingPoints = $user->points()->where([
                    'date' => $date,
                    'event_id' => $participation->event_id,
                    'data_source_id' => $sourceProfile->data_source_id
                ])->sum('amount');
                
                if($existingPoints > 0 && $existingPoints < $distance) { // if existing points are less than distance, then subtract the existing points from the distance
                    Log::debug("Post Data (if): Existing Points - {$existingPoints} || Distance - {$distance} || Modality - {$modality}");
                    $distance = $distance - $existingPoints;
                } else if($existingPoints == $distance) { // if existing points are equal to distance
                    Log::debug("Post Data (else if): Existing Points - {$existingPoints} || Distance - {$distance} || Modality - {$modality}");
                    $distance = 0;
                }
            }
            */

            $pointdata = [
                'amount' => $distance,
                'date' => $date,
                'event_id' => $participation->event_id,
                'modality' => $modality,
                'modality_type' => $modalityType, // auto or manual (manual is for manual entry)
                'data_source_id' => $sourceProfile->data_source_id,
                'transaction_id' => $transactionId
            ];

            $where = [
                'date' => $date,
                'modality' => $modality,
                'event_id' => $participation->event_id,
                'data_source_id' => $sourceProfile->data_source_id,
            ];
            
            if (!$isDailiesCallback) {
                $where['transaction_id'] = $transactionId;
            }

            $userPoint = $user->points()->where($where)->first();

            Log::debug("Point Data: ", [$pointdata]);

            if($userPoint) {
                $userPoint->update($pointdata);
            } else {
                $user->points()->create($pointdata);
            }

            /**
            if(!$userPoint) {
                Log::debug("Point Data that we will save: ", [$pointdata]);
                $user->points()->create($pointdata);
            } else {
                if($userPoint->amount < $pointdata['amount']) {
                    Log::debug("User Point that we will update: ", [$userPoint]);
                    $userPoint->update($pointdata);
                }
            }
            */

            /**
            $userPoint = $user->points()->where([
                'date' => $date,
                'modality' => $modality,
                'event_id' => $participation->event_id,
                'data_source_id' => $sourceProfile->data_source_id]
            )->first();
            
            Log::debug("userPoint: ", [$userPoint]);
    
            if($userPoint) {
                // Add the new distance to the existing distance
                $pointdata['amount'] = $userPoint->amount + $distance;
                
                $userPoint->update($pointdata);
            } else{
                $user->points()->create($pointdata);
            }
            */

            $eventService->createOrUpdateUserPoint($user, $participation->event_id, $date);
            $eventService->userPointWorkflow($user->id, $participation->event_id);

            (new MailService)->sendCelebrationMail($participation->event_id, $user->id);
        }
        
        return true;
    }
    
    public function formatActivities($activities)
    {
        $items = collect($activities)->reduce(function ($data, $item) {
            if (! $item) {
                return $data;
            }

            $key = $item['date'] . '_' . $item['modality'];

            if (isset($data[$key])) {
                $data[$key]['distance'] += $item['distance'];
            } else {
                $data[$key] = $item;
            }

            return $data;

            if (isset($data[$item['date']])) {
                if ($data[$item['date']]['modality'] === $item['modality']) {
                    $data[$item['date']]['distance'] += $item['distance'];
                } else {
                    $data[$item['date']] = array_merge($data[$item['date']], $item);
                }

                return $data;
            }

            $data[$item['date']] = $item;

            return $data;
        }, []);

        return collect($items)->values();
    }


    private function getTimestamp(): int
    {
        return time();
    }

    private function generateNonce(): string
    {
        return Str::random(32);
    }

    private function createSignature($method, $url, $params, $tokenSecret): string
    {
        ksort($params);
        $paramString = http_build_query($params, '', '&', PHP_QUERY_RFC3986);

        $baseString = strtoupper($method).'&'.
            rawurlencode($url).'&'.
            rawurlencode($paramString);

        $signingKey = rawurlencode($this->consumerSecret).'&'.rawurlencode($tokenSecret);

        return rawurlencode(
            base64_encode(
                hash_hmac('sha1', $baseString, $signingKey, true)
            )
        );
    }

    private function buildAuthorizationHeader($params): string
    {
        $headerParams = [];
        foreach ($params as $key => $value) {
            if (strpos($key, 'oauth_') === 0) {
                $headerParams[] = $key.'="'.$value.'"';
            }
        }

        return 'OAuth '.implode(',', $headerParams);
    }

    private function generateApiUrl($endpoint): string
    {
        return $this->healthApiUrl.'/'.$endpoint;
    }

    private function makeGarminRequest(string $method, string $url, array $queryParams = [], ?string $accessToken = null, ?string $accessTokenSecret = null)
    {
        $oauthParams = [
            'oauth_consumer_key' => $this->consumerKey,
            'oauth_nonce' => $this->generateNonce(),
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $this->getTimestamp(),
            'oauth_version' => '1.0',
        ];

        if ($accessToken && $accessTokenSecret) {
            $oauthParams['oauth_token'] = $accessToken;
        }

        // Merge all parameters for signature generation
        $params = array_merge($queryParams, $oauthParams);

        // Generate signature
        $signature = $this->createSignature($method, $url, $params, $accessTokenSecret);
        $oauthParams['oauth_signature'] = $signature;

        // Build authorization header
        $authHeader = $this->buildAuthorizationHeader($oauthParams);

        // Build the full URL with query parameters
        $fullUrl = $url;
        if (! empty($queryParams)) {
            $fullUrl .= '?'.http_build_query($queryParams);
        }

        return Http::withHeaders(['Authorization' => $authHeader])->$method($fullUrl);
    }

    private function getModality(string $modality): string
    {
        return match ($modality) {
            'RUNNING', 'TRACK_RUNNING', 'STREET_RUNNING', 'TREADMILL_RUNNING', 'TRAIL_RUNNING', 'VIRTUAL_RUN', 'INDOOR_RUNNING', 'OBSTACLE_RUN', 'OBSTACLE_RUNNING', 'ULTRA_RUN', 'ULTRA_RUNNING' => 'run',
            'WALKING', 'CASUAL_WALKING', 'SPEED_WALKING' => 'walk',
            'CYCLING', 'CYCLOCROSS', 'DOWNHILL_BIKING', 'INDOOR_CYCLING', 'MOUNTAIN_BIKING', 'RECUMBENT_CYCLING', 'ROAD_BIKING', 'TRACK_CYCLING', 'VIRTUAL_RIDE' => 'bike',
            'SWIMMING', 'LAP_SWIMMING', 'OPEN_WATER_SWIMMING' => 'swim',
            // 'WALKING', 'CASUAL_WALKING', 'SPEED_WALKING', 'GENERIC' => 'daily_steps',
            'HIKING', 'CROSS_COUNTRY_SKIING', 'MOUNTAINEERING', 'ELLIPTICAL', 'STAIR_CLIMBING', 'GENERIC' => 'other'
        };
    }
}
