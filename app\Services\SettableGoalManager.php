<?php

namespace App\Services;

use App\Models\Event;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SettableGoalManager
{
    private User $user;
    private Event $event;

    public function __construct(User $user, Event $event)
    {
        if(!$user && !$event) {
            throw new \Exception("User and event are required");
        }

        $this->user = $user;
        $this->event = $event;

        $this->checkEventParticipation();
    }

    private function checkEventParticipation()
    {
        if(!$this->user->participations()->where('event_id', $this->event->id)->count()) {
            throw new \Exception("User is not participating in this event");
        }
    }

    /**
     * Get the user.
     * 
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * Get the event.
     * 
     * @return Event
     */
    public function getEvent(): Event
    {
        return $this->event;
    }
}